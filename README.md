# LLM Chatbot with GPU Support

A flexible CLI chatbot that supports different LLM models with GPU acceleration using CUDA.

## Features

- 🚀 **GPU Acceleration**: Automatic CUDA detection and GPU utilization
- 🔄 **Model Switching**: Easy switching between different LLM models
- ⚙️ **Configurable**: JSON-based configuration system
- 💬 **Interactive CLI**: User-friendly command-line interface
- 📝 **Conversation History**: Track and review chat history
- 🎛️ **Generation Parameters**: Customizable text generation settings

## Quick Start

1. **Setup Virtual Environment**:
   ```bash
   python -m venv venv
   ./venv/Scripts/activate  # Windows
   # source venv/bin/activate  # Linux/Mac
   ```

2. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the Chatbot**:
   ```bash
   python chatbot.py
   ```

## Usage

### Basic Commands

- `help` - Show available commands
- `info` - Display current model information
- `models` - List available models
- `switch <model>` - Switch to a different model
- `clear` - Clear conversation history
- `history` - Show conversation history
- `quit/exit/q` - Exit the chatbot

### Command Line Options

```bash
python chatbot.py --help
python chatbot.py --model gpt2-medium  # Use specific model
python chatbot.py --no-gpu             # Disable GPU
python chatbot.py --config my_config.json  # Custom config file
```

### Model Switching

The chatbot comes with several pre-configured models:

- `gpt2-large` - GPT-2 Large (774M parameters) - **Default**
- `gpt2-medium` - GPT-2 Medium (355M parameters)
- `gpt2` - GPT-2 Base (124M parameters)
- `distilgpt2` - DistilGPT-2 (82M parameters, faster)

To switch models during chat:
```
switch gpt2-medium
```

To add a new model, edit `chatbot_config.json` or use the configuration API.

## Configuration

The chatbot uses `chatbot_config.json` for configuration. Key settings:

```json
{
  "current_model": "openai-community/gpt2-large",
  "use_gpu": true,
  "generation_params": {
    "max_new_tokens": 100,
    "temperature": 0.7,
    "top_p": 0.9,
    "do_sample": true
  }
}
```

## GPU Requirements

- NVIDIA GPU with CUDA support
- CUDA 11.8 (or compatible version)
- Sufficient GPU memory for the model

## Files

- `chatbot.py` - Main CLI chatbot interface
- `model_manager.py` - LLM model management and loading
- `config.py` - Configuration system
- `chatbot_config.json` - Configuration file (auto-generated)
- `requirements.txt` - Python dependencies

## Adding New Models

You can easily add new models by editing the configuration:

```python
from config import ChatbotConfig

config = ChatbotConfig()
config.add_model(
    alias="llama2",
    model_name="meta-llama/Llama-2-7b-chat-hf",
    description="Llama 2 7B Chat model"
)
```

Or directly edit `chatbot_config.json`:

```json
{
  "available_models": {
    "custom-model": {
      "name": "path/to/your/model",
      "description": "Your custom model",
      "type": "causal_lm"
    }
  }
}
```
