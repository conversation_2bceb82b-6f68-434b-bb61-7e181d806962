../../Scripts/accelerate-config.exe,sha256=qLWjALsKXnnAKgXbLepXprA0xEb-GrMdBl31-bF4HWc,106355
../../Scripts/accelerate-estimate-memory.exe,sha256=7RTIfHUoOBTA2CVpkbQSs1lpiV0lbhT2S4onba5Kues,106357
../../Scripts/accelerate-launch.exe,sha256=YKa1PEv-hhDR-lItdZSdsgA_zFrVJ_4T-OiLp1fqjfc,106355
../../Scripts/accelerate-merge-weights.exe,sha256=2jW9dipCqIn_OdSYprgS28DY7pMFTqpzQiW2YkSNJ5I,106354
../../Scripts/accelerate.exe,sha256=aLuM4CPkMe0WawstWj-liLKVWUM-OrFM_k11fCCXLJo,106363
accelerate-1.8.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
accelerate-1.8.1.dist-info/METADATA,sha256=VUDv08VTjcXDNtu0bFGUz11j_VBolsghyrYwaBrDUyM,19817
accelerate-1.8.1.dist-info/RECORD,,
accelerate-1.8.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
accelerate-1.8.1.dist-info/WHEEL,sha256=lTU6B6eIfYoiQJTZNc-fyaR6BpL6ehTzU3xGYxn2n8k,91
accelerate-1.8.1.dist-info/entry_points.txt,sha256=Vpy8gUGfZ-1VnM2229fb8CpJNLBdMH_wtJ9PQ7b_2tQ,296
accelerate-1.8.1.dist-info/licenses/LICENSE,sha256=xx0jnfkXJvxRnG63LTGOxlggYnIysveWIZ6H3PNdCrQ,11357
accelerate-1.8.1.dist-info/top_level.txt,sha256=esVfdxTidsjQ90zsN_rPpjLFJ4ijRlx4mnLrG09hlt4,11
accelerate/__init__.py,sha256=x-IpfrXM6_IliRXhu0SgO4DkU_r7EMSnScbEswWPbBU,1504
accelerate/__pycache__/__init__.cpython-39.pyc,,
accelerate/__pycache__/accelerator.cpython-39.pyc,,
accelerate/__pycache__/big_modeling.cpython-39.pyc,,
accelerate/__pycache__/checkpointing.cpython-39.pyc,,
accelerate/__pycache__/data_loader.cpython-39.pyc,,
accelerate/__pycache__/hooks.cpython-39.pyc,,
accelerate/__pycache__/inference.cpython-39.pyc,,
accelerate/__pycache__/launchers.cpython-39.pyc,,
accelerate/__pycache__/local_sgd.cpython-39.pyc,,
accelerate/__pycache__/logging.cpython-39.pyc,,
accelerate/__pycache__/memory_utils.cpython-39.pyc,,
accelerate/__pycache__/optimizer.cpython-39.pyc,,
accelerate/__pycache__/scheduler.cpython-39.pyc,,
accelerate/__pycache__/state.cpython-39.pyc,,
accelerate/__pycache__/tracking.cpython-39.pyc,,
accelerate/accelerator.py,sha256=Rq60sftkvjpiBlBLfhvWlvXuGFfSXwBLEIqeHECrAQg,179209
accelerate/big_modeling.py,sha256=bqqO4Lw00YErTup7MUM6saOVHRFvMNOdNqAwg96fBG0,34765
accelerate/checkpointing.py,sha256=ARi8ZNFlLQW2uz9hd2D5-5rRUNoY2zL7BSe8HQKBFjs,13957
accelerate/commands/__init__.py,sha256=m1PPTDT4ziIAvM0-FDSgIMIZ69Konn126s6LwuzH6v8,606
accelerate/commands/__pycache__/__init__.cpython-39.pyc,,
accelerate/commands/__pycache__/accelerate_cli.cpython-39.pyc,,
accelerate/commands/__pycache__/env.cpython-39.pyc,,
accelerate/commands/__pycache__/estimate.cpython-39.pyc,,
accelerate/commands/__pycache__/launch.cpython-39.pyc,,
accelerate/commands/__pycache__/merge.cpython-39.pyc,,
accelerate/commands/__pycache__/test.cpython-39.pyc,,
accelerate/commands/__pycache__/to_fsdp2.cpython-39.pyc,,
accelerate/commands/__pycache__/tpu.cpython-39.pyc,,
accelerate/commands/__pycache__/utils.cpython-39.pyc,,
accelerate/commands/accelerate_cli.py,sha256=SkwFad6Z1ZsGjtm7TiXFq8je-akshp_0WxX_6rGSBw8,1972
accelerate/commands/config/__init__.py,sha256=iJK8dgj3pc5Vdr1E7UuGoFu-BlybyXLxYDoTg9gXngE,1645
accelerate/commands/config/__pycache__/__init__.cpython-39.pyc,,
accelerate/commands/config/__pycache__/cluster.cpython-39.pyc,,
accelerate/commands/config/__pycache__/config.cpython-39.pyc,,
accelerate/commands/config/__pycache__/config_args.cpython-39.pyc,,
accelerate/commands/config/__pycache__/config_utils.cpython-39.pyc,,
accelerate/commands/config/__pycache__/default.cpython-39.pyc,,
accelerate/commands/config/__pycache__/sagemaker.cpython-39.pyc,,
accelerate/commands/config/__pycache__/update.cpython-39.pyc,,
accelerate/commands/config/cluster.py,sha256=Ln8AbiHX0fDLYV8Gxk3WjC2JL94CEAjuhhn6BKdQ1zE,37544
accelerate/commands/config/config.py,sha256=FuRlQvOjgATEtyqOSsGD-KEtOCvACOHjs2C-krrtldk,3035
accelerate/commands/config/config_args.py,sha256=hJo7E3J_T7MPNkgtyNjPilJuH-wJK09mGWF6CvGFYIQ,9970
accelerate/commands/config/config_utils.py,sha256=lbV9iKAVSF5bUyQDGpzFduhH5JENYgAilPpRLLlCEq8,3299
accelerate/commands/config/default.py,sha256=UY0dQ7w1peBkQe8QDCm3XJrTnsRcm_G-_hf44NzNAbU,6213
accelerate/commands/config/sagemaker.py,sha256=SRLup9XrV_Z-23A3d8gZbkqbonDcscMDDmbpmytCzbY,10636
accelerate/commands/config/update.py,sha256=NXW1J7GkUHpg71QlIXsmMB_0z8S8IZo2FWax5POwrhc,2395
accelerate/commands/env.py,sha256=gRGwtPhMMWmZ5NTMaMrgHXaOzBV6SvAtP78TuuJvC6g,4336
accelerate/commands/estimate.py,sha256=Qduq4xudVyIede37BMEe1rNhXf-rfW-MHV2KtwxdfEA,12585
accelerate/commands/launch.py,sha256=h-d1W1y6NEwciGdzeLY-aTQG2N6dx3iBgcwSZZ_Yjn0,46889
accelerate/commands/menu/__init__.py,sha256=uqSlBM0TFHBwzdv3p3SXfpAk1lZFp4h1a7mbBdscPHs,645
accelerate/commands/menu/__pycache__/__init__.cpython-39.pyc,,
accelerate/commands/menu/__pycache__/cursor.cpython-39.pyc,,
accelerate/commands/menu/__pycache__/helpers.cpython-39.pyc,,
accelerate/commands/menu/__pycache__/input.cpython-39.pyc,,
accelerate/commands/menu/__pycache__/keymap.cpython-39.pyc,,
accelerate/commands/menu/__pycache__/selection_menu.cpython-39.pyc,,
accelerate/commands/menu/cursor.py,sha256=-lmpJVAzvNc0c3EOtSuLoKB59zqylVCbYyWLPnrOmvQ,2028
accelerate/commands/menu/helpers.py,sha256=KrSB5fJjH4MUEUAQJ6bYaN16AYcnl9UalDrPD3DYeeg,1483
accelerate/commands/menu/input.py,sha256=T8Mdd-Y_OURgqfDV9qZh4Wf6hmT22AneNtJzj4JA1Rk,2512
accelerate/commands/menu/keymap.py,sha256=eXj-suyYs1m5dEHoUKN4mKAMLc8DWHnwhP6G6JSU0jQ,4086
accelerate/commands/menu/selection_menu.py,sha256=bxy-DHaKKC6SCToOlMBv5_z0MdUzylEg6Sio9OuV3GM,4921
accelerate/commands/merge.py,sha256=3Uo14LADvk8RJwZ_i6kIsGwWB3Ul5y8Uq47oHkDYqKU,2387
accelerate/commands/test.py,sha256=YrPYEaAACOGZ6btn2MV6NbMSEdBUcMWADLbQWaZSHtk,2149
accelerate/commands/to_fsdp2.py,sha256=gfbhoUT4qFB3LVDMNmckElgLG0yWm8aj_aofszeiJmM,5991
accelerate/commands/tpu.py,sha256=KyxDP7IuveidZrbW4rx2s8Ku3o_ptI6tzwr_R7ck0os,5548
accelerate/commands/utils.py,sha256=aT8xUCe2pCkFII7yZxcfaohEjgBAzMUM7WiD4UuWSOY,4150
accelerate/data_loader.py,sha256=PUvNjsxrgYXxvPOqArRpYATFrbeC7rXzBpQOuDJuZ_U,65294
accelerate/hooks.py,sha256=y4eN7blCjOkXY9_mVBmkgiTKqJfasAKrHOddEHj91Vg,33293
accelerate/inference.py,sha256=NLANdzXm5PwmDWbPYkFmoRoQSLLvuhfvIG33xfpapT0,7668
accelerate/launchers.py,sha256=GCj7y4qgK34k-VWi6EeM0DvXfsHBeGOZBfgTv9wi1zE,13724
accelerate/local_sgd.py,sha256=aCj_yqXK_FhhZRWEpzXIkgXBERH6fC3HyrC3nsOj1uA,4160
accelerate/logging.py,sha256=4XcgY_BV7Qn_enh2tZ-8fNtuaE_3n-LsYJbgwhRx_PI,5042
accelerate/memory_utils.py,sha256=3R5LoeHl6GgTZ-IMPrDZMdaEehWarGdPqODushb-6pg,862
accelerate/optimizer.py,sha256=p5qfCOMi8DAjvx6eECypo4ej3pvmQ_U_bgmrEAKc4hY,8158
accelerate/scheduler.py,sha256=des_4M_Tt1W8gCYZZbLla0GHBEgJY3Wx2EGBQPTzeiY,4238
accelerate/state.py,sha256=NpBKZQAn1FsrVVC0j_s_JocrVa-XWPQAxwog6cwNvw4,56351
accelerate/test_utils/__init__.py,sha256=ROl3bvBqmuI6ueRlPRNkNC-83yb6u6z_FFzrHJtq8rM,1809
accelerate/test_utils/__pycache__/__init__.cpython-39.pyc,,
accelerate/test_utils/__pycache__/examples.cpython-39.pyc,,
accelerate/test_utils/__pycache__/testing.cpython-39.pyc,,
accelerate/test_utils/__pycache__/training.cpython-39.pyc,,
accelerate/test_utils/examples.py,sha256=CO3SlkkrORUjroreoPfBPwfcNCDlHSgNWIt81iz2oQ8,7257
accelerate/test_utils/scripts/__init__.py,sha256=m1PPTDT4ziIAvM0-FDSgIMIZ69Konn126s6LwuzH6v8,606
accelerate/test_utils/scripts/__pycache__/__init__.cpython-39.pyc,,
accelerate/test_utils/scripts/__pycache__/test_cli.cpython-39.pyc,,
accelerate/test_utils/scripts/__pycache__/test_ddp_comm_hook.cpython-39.pyc,,
accelerate/test_utils/scripts/__pycache__/test_distributed_data_loop.cpython-39.pyc,,
accelerate/test_utils/scripts/__pycache__/test_merge_weights.cpython-39.pyc,,
accelerate/test_utils/scripts/__pycache__/test_notebook.cpython-39.pyc,,
accelerate/test_utils/scripts/__pycache__/test_ops.cpython-39.pyc,,
accelerate/test_utils/scripts/__pycache__/test_script.cpython-39.pyc,,
accelerate/test_utils/scripts/__pycache__/test_sync.cpython-39.pyc,,
accelerate/test_utils/scripts/external_deps/__init__.py,sha256=m1PPTDT4ziIAvM0-FDSgIMIZ69Konn126s6LwuzH6v8,606
accelerate/test_utils/scripts/external_deps/__pycache__/__init__.cpython-39.pyc,,
accelerate/test_utils/scripts/external_deps/__pycache__/test_checkpointing.cpython-39.pyc,,
accelerate/test_utils/scripts/external_deps/__pycache__/test_ds_multiple_model.cpython-39.pyc,,
accelerate/test_utils/scripts/external_deps/__pycache__/test_metrics.cpython-39.pyc,,
accelerate/test_utils/scripts/external_deps/__pycache__/test_peak_memory_usage.cpython-39.pyc,,
accelerate/test_utils/scripts/external_deps/__pycache__/test_performance.cpython-39.pyc,,
accelerate/test_utils/scripts/external_deps/__pycache__/test_pippy.cpython-39.pyc,,
accelerate/test_utils/scripts/external_deps/__pycache__/test_zero3_integration.cpython-39.pyc,,
accelerate/test_utils/scripts/external_deps/test_checkpointing.py,sha256=XHaNRmnrARd1izXFjWGi5UjYGas-4vqayW51jAHBPCA,10699
accelerate/test_utils/scripts/external_deps/test_ds_multiple_model.py,sha256=Cg4-h0B4UcOQ5CxXjIdrsPVR5fFsWCv24DqZGjXEwW8,13790
accelerate/test_utils/scripts/external_deps/test_metrics.py,sha256=Ev2XKaiwmznoxKujskAAuISGChW646MOiyf0CXEPb9Y,12168
accelerate/test_utils/scripts/external_deps/test_peak_memory_usage.py,sha256=9Yn9Rc7d-yWr1fU0RagASPG5l8vrKeHVYbuYABbA-fU,12498
accelerate/test_utils/scripts/external_deps/test_performance.py,sha256=4SW108BHEdpzDA_VY4B0GKKAdms4QxVlVywhZ-CZwRI,11721
accelerate/test_utils/scripts/external_deps/test_pippy.py,sha256=rYF5bqSCe_pfylxDv2_2Q7S2mHIb9_6fGns-IcLgE_E,4789
accelerate/test_utils/scripts/external_deps/test_zero3_integration.py,sha256=P9alBOHZ9Lfqs5LoRP7bCbXl-tnsNrBkvJZGseibBeA,1665
accelerate/test_utils/scripts/test_cli.py,sha256=-p78NTJgKdeTNDqyrQuGEZZqMpnzdUHPyWZ05FMl5EI,1081
accelerate/test_utils/scripts/test_ddp_comm_hook.py,sha256=k_-2MBjLKNdMGIcneTbuGd84K05Wp1GEQX6DUVF9UBw,3566
accelerate/test_utils/scripts/test_distributed_data_loop.py,sha256=RUWTwd7DIpr2fl7JtKOsvTjMiJioTxO8FdSr2Lw_5uI,15137
accelerate/test_utils/scripts/test_merge_weights.py,sha256=UQa5cR_gZK94fNuopsQ4FEAfTNEsTnlepCxXhboGEOg,6144
accelerate/test_utils/scripts/test_notebook.py,sha256=qfIy3IvH74-kGn8nadBn_k7qrviqvsxy5ijsnUhuY6o,3894
accelerate/test_utils/scripts/test_ops.py,sha256=Bcs-h8EMJwULTfbizlFN5qkv3JraWEpoSZWMn-HswiI,6265
accelerate/test_utils/scripts/test_script.py,sha256=zns5-YNj5h3YHBk79XisiqDQfX2nl9UL8K_NS5IoqFg,36446
accelerate/test_utils/scripts/test_sync.py,sha256=PDe8sYZLCL2LKjj_L9b-Bh2BjAjeii9EZ8sZNfuYx5s,18817
accelerate/test_utils/testing.py,sha256=Q3kzLnpna3YhAyM6fUDQ7477LtJN9Qb0bNgj6b6V6ik,29241
accelerate/test_utils/training.py,sha256=jO5YEIr34jAcnJ_9WNp_x3zuHzSam_I6IgMvmcGm7yI,6456
accelerate/tracking.py,sha256=Bc5DPSIlexbiV4SryKl-UxwH1CeEnOZkbGwYvvwLfmw,48032
accelerate/utils/__init__.py,sha256=xl6E87k9XJH4imIBQ61Qaip0_1JWx9Or2_tCmSHrTJI,7980
accelerate/utils/__pycache__/__init__.cpython-39.pyc,,
accelerate/utils/__pycache__/ao.cpython-39.pyc,,
accelerate/utils/__pycache__/bnb.cpython-39.pyc,,
accelerate/utils/__pycache__/constants.cpython-39.pyc,,
accelerate/utils/__pycache__/dataclasses.cpython-39.pyc,,
accelerate/utils/__pycache__/deepspeed.cpython-39.pyc,,
accelerate/utils/__pycache__/environment.cpython-39.pyc,,
accelerate/utils/__pycache__/fsdp_utils.cpython-39.pyc,,
accelerate/utils/__pycache__/imports.cpython-39.pyc,,
accelerate/utils/__pycache__/launch.cpython-39.pyc,,
accelerate/utils/__pycache__/megatron_lm.cpython-39.pyc,,
accelerate/utils/__pycache__/memory.cpython-39.pyc,,
accelerate/utils/__pycache__/modeling.cpython-39.pyc,,
accelerate/utils/__pycache__/offload.cpython-39.pyc,,
accelerate/utils/__pycache__/operations.cpython-39.pyc,,
accelerate/utils/__pycache__/other.cpython-39.pyc,,
accelerate/utils/__pycache__/random.cpython-39.pyc,,
accelerate/utils/__pycache__/rich.cpython-39.pyc,,
accelerate/utils/__pycache__/torch_xla.cpython-39.pyc,,
accelerate/utils/__pycache__/tqdm.cpython-39.pyc,,
accelerate/utils/__pycache__/transformer_engine.cpython-39.pyc,,
accelerate/utils/__pycache__/versions.cpython-39.pyc,,
accelerate/utils/ao.py,sha256=yfi84ywNpXsKZ6di4jbmtd6bIvRi9bmygr3giN92_Pg,4777
accelerate/utils/bnb.py,sha256=PdgdXwjhQ4Sg9orj4hlUiMQlPxSsdsiNfFVq4yBtl-g,20651
accelerate/utils/constants.py,sha256=A5KmcIry76B9p4Fa8CuI5ugv7bVcyTc5g4IBU1cvky0,3516
accelerate/utils/dataclasses.py,sha256=ByhYIuIRt92un7Uj9Lr3gbClBzsOaGBnNvgpaBlF2-k,134173
accelerate/utils/deepspeed.py,sha256=gt-jwOpOtRs6N4sohuSO2I3x1qLoO6uYYjnPrKp6Zk4,14796
accelerate/utils/environment.py,sha256=h0zacbBkAp9szltTf5-aTr5NcbVsQp7wl6DFWp8XNuI,15257
accelerate/utils/fsdp_utils.py,sha256=uBd3bovBPxa2gt5Q-L-7isvsZdi2b7_2H8jzm8dfHsw,36206
accelerate/utils/imports.py,sha256=w4DqTh21D_rsCBdifSm27vrilk_VZtMPWvf1U439qhs,16598
accelerate/utils/launch.py,sha256=-QfklFN8U0U37dQZiz70GGTFSQ0vzadBW7ZFShJykrc,33406
accelerate/utils/megatron_lm.py,sha256=ZMxcYAdGnMWla2PtB04ctb9mhMfKUP8qLYusGNjyScU,58054
accelerate/utils/memory.py,sha256=CDG80xzDQfQOqhP56jZ5xVN5bLIgNDqj0TtcoiidvhY,7201
accelerate/utils/modeling.py,sha256=m3ZSeTZjI83MpdtGK9hdtp3jyGAIKn3tPymHAAM-mJU,94721
accelerate/utils/offload.py,sha256=VFaL8oSJzqZ_47VuUQ69xZi9bF2heRSFoOSnnOxbGXc,7825
accelerate/utils/operations.py,sha256=84SpXwKsbNHHePboL9gUqK6bA5vo0kyH_gLAdwnzE_s,31266
accelerate/utils/other.py,sha256=LQTH_iRAGyTeGZS19y-1hyJTboFN2n-2sqd8fc_Bats,19676
accelerate/utils/random.py,sha256=Xv_ZJm9eaC2Q7rgZy9OpOunKuTingMiDQCH00qhNVxE,6220
accelerate/utils/rich.py,sha256=8JZX_uGMQX-BufdXxJpdne7BWd1KyLHSgbiGxrDMYr8,847
accelerate/utils/torch_xla.py,sha256=Pq1tuqN0X_pWDVza6YgjfO45uoJdoRVRForLeLQzFus,1908
accelerate/utils/tqdm.py,sha256=k8e9JnieTEQHCCNBaiBys7hPxWlEbyRASdIma-qy_X8,1657
accelerate/utils/transformer_engine.py,sha256=PENq1zhDzENnFFfV4hLzhZiiLo28tQFvmuhpQPlc3S0,6594
accelerate/utils/versions.py,sha256=UgmcbjBm--6CIx1ZamSAMjAK_B_2l48LbeaNygqej8M,2149
